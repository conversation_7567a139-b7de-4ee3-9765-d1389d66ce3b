2025-06-05 13:38:07.043 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 13:38:07.044 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 13:38:08.159 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 13:38:08.159 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 13:38:08.161 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 13:38:08.824 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 13:38:09.569 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 13:38:09.569 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 13:38:28.215 | e2314c5673e84d1c81e26c4ccd027fa6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:28.458 | 3fe09eb6b3e3428db7140f5ec810010a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:38.616 | 01e407e3858746f984d34296dd567ea2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:38.936 | d4df64e76614443eb131da823a80bd43 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:39.650 | 32fefd93846a4f9997ea7e8cecf109a7 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.881 | b46827bb957d4a7498ef3db09b63afc5 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.955 | 602734b7617040bb91c2b4e7e7d48443 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:38:45.594 | 878ea43676b64f23a234783cf1e3d199 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.595 | 28e68eff23db4f0f9a1456bd8476e708 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.627 | a46da5c54262468dbd6a7faffc0a7aca | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:39:14.641 | 42e67e9a5a9a4dc681e329f955775cb5 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 14:19:11.893 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 14:19:11.893 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 14:19:13.244 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 14:19:13.245 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 14:19:13.246 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 14:19:13.871 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 14:19:14.513 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 14:19:14.515 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 14:19:35.425 | 6bc4e1a438cb43bdbc6c40c65a0b5ac9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-05 14:19:35.483 | f487fca699b540e08dc92a59fcf948d0 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-05 14:19:35.550 | 5233fe02108743e6beceba5ddefaba27 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为16b9e53c-5eb5-421a-8f57-f4a8fe6fe105的会话获取图片验证码成功
2025-06-05 14:19:53.699 | a89a85525bfc4542b2726e8e394122d8 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 14:19:53.953 | 76e6556166a243c899c05b97239e4ff2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:19:54.181 | c955186852fd43fe9357b8b4010e553f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:20:07.102 | a916a42b4ae949269d605c98523a6deb | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 14:20:07.260 | 3eefbad70fe1402fbe46e41273862057 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 14:20:07.327 | 7fd39f95308c44579934594adde76254 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 14:40:43.177 | c9e82b848cca4b06b75f45c9609d43d5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:40:43.433 | c925598f85214739b8df3109d02dfe9f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:41:11.350 | d1d82f0688344760b71288f7d5e6a0f0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:41:11.682 | d78c077b29944a2486ad5c6480b425c6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:58:45.691 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 14:58:45.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 14:58:55.526 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 14:58:55.526 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 14:58:57.041 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 14:58:57.041 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 14:58:57.043 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 14:58:57.811 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 14:58:58.633 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 14:58:58.634 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 14:59:06.289 | 6e092fe9a02846408e68a45fb7bc229c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:59:06.631 | d2f2150918084e17940ba70b8aa56328 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:03:12.091 | 3422cd57d62445929b8156d0dd2d3b71 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:03:12.359 | 7e1adf481a7244d29c62b1fa371b6dff | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:10:28.113 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:10:28.113 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:10:29.252 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:10:29.252 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:10:29.254 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:10:29.964 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:10:30.769 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:10:30.769 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:10:39.633 | 416992dfcf78426f804be0a70e64459f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:10:39.898 | 274d02ec41044d6aa07140741b86f759 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:40:06.598 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:40:06.599 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:40:07.780 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:40:07.780 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:40:07.782 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:40:08.483 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:40:09.141 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:40:09.141 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:40:33.628 | 1e6b4194ce374c508e351dc6891c10dc | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为a633d6a8-9666-4df9-97d0-5bfd0062620f的会话获取图片验证码成功
2025-06-05 15:40:36.845 | 7a5bb88c86dd45a181e7e921b1e94398 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 15:40:37.161 | e2df14fdfcc7450fa9b817afc186b89a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:40:37.524 | c93c87ecafa34e329f6436c09f600000 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 16:29:08.393 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:29:08.396 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:29:11.112 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:29:11.113 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:29:12.167 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:29:12.168 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:29:12.169 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:29:12.790 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:29:13.468 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:29:13.468 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:29:30.462 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:29:30.462 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:29:33.100 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:29:33.101 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:29:34.050 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:29:34.051 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:29:34.053 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:29:34.588 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:29:35.377 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:29:35.377 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:30:21.227 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:30:21.228 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:30:23.746 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:30:23.747 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:30:24.738 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:30:24.738 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:30:24.740 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:30:25.232 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:30:25.855 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:30:25.855 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:31:37.548 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:31:37.549 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:31:40.021 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:31:40.022 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:31:41.323 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:31:41.323 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:31:41.325 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:31:42.079 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:31:42.721 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:31:42.721 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:32:06.953 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:32:06.954 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:32:09.518 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:32:09.519 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:32:10.651 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:32:10.652 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:32:10.653 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:32:11.334 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:32:12.110 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:32:12.110 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:32:58.267 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:32:58.268 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:43:48.652 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:43:48.653 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:43:49.990 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:43:49.991 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:43:49.992 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:43:50.759 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:43:51.464 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:43:51.465 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:50:56.459 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:50:56.459 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:50:59.065 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:50:59.066 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:51:00.422 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:51:00.422 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:51:00.425 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:51:01.267 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:51:02.057 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:51:02.057 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:51:59.247 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:51:59.248 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:52:01.561 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:52:01.562 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:52:02.725 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:52:02.726 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:52:02.727 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:52:03.306 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:52:04.189 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:52:04.190 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:53:51.273 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:53:51.273 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:53:53.856 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:53:53.857 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:53:55.341 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:53:55.341 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:53:55.343 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:53:56.204 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:53:57.159 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:53:57.159 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:54:49.428 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:54:49.428 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:54:51.882 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:54:51.883 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:54:53.157 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:54:53.157 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:54:53.169 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:54:54.102 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:54:55.009 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:54:55.009 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:55:45.783 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:55:45.783 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:55:48.215 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:55:48.216 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:55:49.698 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:55:49.698 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:55:49.700 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:55:50.424 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:55:51.734 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:55:51.734 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:56:22.001 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:56:22.003 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:56:24.482 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:56:24.483 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:56:25.490 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:56:25.491 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:56:25.493 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:56:25.979 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:56:26.569 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:56:26.569 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:56:44.058 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:56:44.059 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:56:46.386 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:56:46.387 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:56:47.715 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:56:47.715 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:56:47.717 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:56:48.363 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:56:49.027 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:56:49.028 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:57:44.019 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:57:44.020 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:57:46.390 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:57:46.391 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:57:47.812 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:57:47.812 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:57:47.814 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:57:48.600 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:57:49.523 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:57:49.523 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:59:01.317 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:59:01.317 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:59:03.844 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:59:03.845 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:59:06.632 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:59:06.632 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:59:06.634 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:59:07.170 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:59:07.928 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:59:07.929 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:59:46.333 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:59:46.333 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:59:49.075 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:59:49.076 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:59:50.053 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:59:50.053 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:59:50.055 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:59:50.673 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:59:51.490 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:59:51.490 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:01:13.940 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:01:13.941 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:01:16.281 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 17:01:16.282 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:01:17.239 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:01:17.240 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:01:17.241 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:01:17.732 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:01:18.496 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:01:18.496 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:04:21.061 | 14fc46183dc9410183da149002aa7325 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-05 17:04:21.146 | fdda5f318d0d49c6b2b60c293196a659 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-05 17:04:21.221 | f679ae8c9e32429b85703a0098320f3c | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为fb00eb6d-fcd9-4b95-86db-23aab040543c的会话获取图片验证码成功
2025-06-05 17:04:24.603 | 0693ed0be025443589fcc3898a590ba7 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 17:04:24.833 | d41eafb9ed054ec3a8ea0c99d39f674e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 17:04:25.105 | ca3c4a3485da4fc388dfa7252c63204f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 17:10:04.677 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:10:04.678 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:10:07.357 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 17:10:07.357 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:10:08.847 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:10:08.847 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:10:08.849 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:10:09.761 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:10:10.688 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:10:10.688 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:11:09.300 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:11:09.301 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:11:11.851 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 17:11:11.852 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:11:12.906 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:11:12.906 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:11:12.908 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:11:13.441 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:11:14.183 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:11:14.183 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:12:00.611 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:12:00.612 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:12:03.191 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 17:12:03.192 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:12:05.119 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:12:05.119 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:12:05.120 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:12:05.942 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:12:06.587 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:12:06.587 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:12:50.122 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:12:50.122 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:12:52.691 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 17:12:52.692 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:12:53.776 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:12:53.776 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:12:53.778 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:12:54.355 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:12:55.113 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:12:55.114 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 17:13:35.823 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:13:35.824 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:13:38.241 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:13:38.242 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:13:39.092 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:13:39.093 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:13:39.095 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:13:39.543 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:13:40.203 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:13:40.203 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:14:17.799 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:14:17.800 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:14:20.135 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:14:20.136 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:14:21.200 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:14:21.201 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:14:21.213 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:14:21.770 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:14:22.411 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:14:22.411 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:19:13.846 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:19:13.847 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:19:16.266 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:19:16.267 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:19:17.373 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:19:17.373 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:19:17.375 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:19:17.971 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:19:18.966 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:19:18.966 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:20:06.230 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:20:06.230 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:20:08.676 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:20:08.677 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:20:09.655 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:20:09.655 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:20:09.656 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:20:10.166 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:20:10.908 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:20:10.908 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:21:48.322 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:21:48.323 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:21:50.642 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:21:50.643 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:21:51.906 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:21:51.906 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:21:51.908 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:21:52.618 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:21:53.542 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:21:53.542 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:25:01.139 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:25:01.140 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:25:03.638 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:25:03.638 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:25:04.801 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:25:04.802 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:25:04.803 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:25:05.358 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:25:06.089 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:25:06.090 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:28:28.848 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:28:28.849 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 17:28:31.437 |  | INFO     | server:lifespan:36 - RuoYi-FastAPI开始启动
2025-06-05 17:28:31.439 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 17:28:32.517 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 17:28:32.517 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 17:28:32.518 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 17:28:33.131 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 17:28:33.802 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 17:28:33.802 |  | INFO     | server:lifespan:43 - RuoYi-FastAPI启动成功
2025-06-05 17:29:56.356 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 17:29:56.357 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
